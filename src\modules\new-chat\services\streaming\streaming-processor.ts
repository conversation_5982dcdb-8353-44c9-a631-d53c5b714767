"use client";
import { processStreamLine } from "../../lib/streaming-service.lib";
import { IStreamingOptions } from "../../types/streaming.type";

export class StreamProcessor {
	private readonly CHUNK_DELAY = 50; // 50ms delay between chunk processing

	async process(body: ReadableStream<Uint8Array>, options: IStreamingOptions) {
		const reader = body.getReader();
		const decoder = new TextDecoder();
		let buffer = "";

		try {
			while (true) {
				const { done, value } = await reader.read();
				if (done) break;

				const chunk = decoder.decode(value, { stream: true });
				buffer += chunk;

				// Processa o buffer para extrair linhas completas ou chunks JSON
				await this.processBuffer(buffer, options, remainingBuffer => {
					buffer = remainingBuffer;
				});
			}

			// Processa o buffer restante, se houver
			if (buffer.trim()) {
				console.log("processando buffer final:", buffer.trim());
				await this.processSingleLine(buffer.trim(), options);
			}
		} finally {
			reader.releaseLock();
		}
	}

	private async processBuffer(buffer: string, options: IStreamingOptions, updateBuffer: (remaining: string) => void) {
		let processed = false;
		let currentBuffer = buffer;

		while (!processed) {
			const newlineIndex = currentBuffer.indexOf("\n");
			if (newlineIndex !== -1) {
				// Encontrou uma linha completa
				const line = currentBuffer.substring(0, newlineIndex).trim();
				currentBuffer = currentBuffer.substring(newlineIndex + 1);

				if (line) {
					await this.processSingleLine(line, options);
					// Pequeno delay para simular streaming mais natural
					await this.delay(this.CHUNK_DELAY);
				}
			} else {
				// Não há mais linhas completas no buffer
				processed = true;
			}
		}

		updateBuffer(currentBuffer);
	}

	private async processSingleLine(line: string, options: IStreamingOptions) {
		console.log("processando linha:", line);
		const result = processStreamLine(line);

		if (result && typeof result.content === "string") {
			// Processa imediatamente cada chunk
			if (result.type) {
				options.onChunk?.({ content: result.content, type: result.type });
			} else {
				options.onChunk?.({ content: result.content });
			}
		}
	}

	private delay(ms: number): Promise<void> {
		return new Promise(resolve => setTimeout(resolve, ms));
	}
}
