// // services/AIClient.ts
// export class AIClient {
// 	private token: string;

// 	constructor(baseUrl: string, token: string) {
// 		// this.baseUrl = baseUrl;
// 		this.token = token;
// 	}

// 	/**
// 	 * Faz a requisição para a API de IA e retorna o texto concatenado
// 	 */
// 	async ask(message: string): Promise<string> {
// 		const response = await fetch(`http://**************:10/ai/ask`, {
// 			method: "POST",
// 			headers: {
// 				accept: "*/*",
// 				Authorization: `Bearer eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICIxSl8xNmJxWThWMU9tUDdWYTI2OFRXUThrOHlfVXp0WThPZmtpMzlwb2g4In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.k9zN5pCKKfHJ8d0FAaZ4yf2ZHWZsS3hgtp4e6tz12uJJczy1ZEN_50AsK6YaC7h6QisNmoUX2vHyz7O-aN-bhY7m8_FxvxxKJBXqQUSDJdgHW3i-bT7uaUYo6GwDpkhdNxvyPZ3LF1XgmQpI5q2gexzw_1aS8bbfFqiSFcv4DBLxQX7zY2Hi_zY09mqqYD6Oyy2PEa9CLsVUNNnR0-YIH87_aBcEm4Oz61cKO9Iuu0WtxRwnFjNC0nThFR55N30X_ZT_QoIEuO6j22I-IZVcy27LKRUqyO0honrn4kBZbHF74Kdebe4lVBfjRp6C53hT4sZ9VOFc5IUPHha4OnSYkw`,
// 				"Content-Type": "application/json",
// 			},
// 			body: JSON.stringify({ message }),
// 		});

// 		if (!response.body) {
// 			throw new Error("Resposta não contém body");
// 		}

// 		const reader = response.body.getReader();
// 		const decoder = new TextDecoder("utf-8");

// 		let result = "";

// 		while (true) {
// 			const { done, value } = await reader.read();
// 			if (done) break;

// 			const chunk = decoder.decode(value, { stream: true });

// 			// Cada linha começa com "data: " — vamos filtrar só conteúdo válido
// 			for (const line of chunk.split("\n")) {
// 				if (line.startsWith("data: ")) {
// 					try {
// 						const parsed = JSON.parse(line.replace("data: ", ""));
// 						if (parsed.content) {
// 							result += parsed.content;
// 						}
// 					} catch {
// 						// Ignora linhas que não são JSON válido
// 					}
// 				}
// 			}
// 		}

// 		return result.trim();
// 	}
// }

// export const clientIA
