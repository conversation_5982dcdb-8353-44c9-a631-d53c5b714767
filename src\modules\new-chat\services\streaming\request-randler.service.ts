"use client";
import { getAllCookies } from "../../../../shared/lib/cookies/crud/get";
import { CookieHeaderService } from "../../../../shared/lib/cookies/services/cookie-header.service";
import { IChatStreamRequest, IStreamingOptions } from "../../types/streaming.type";
import { StreamErrorHandler } from "./error-handler.service";
import { StreamProcessor } from "./streaming-processor";
import { StreamUrlBuilder } from "./url-builder.service";

export class StreamRequestHandler {
	private readonly chunkTimeout = 30000;
	private readonly maxRetries = 3;

	constructor(
		private processor: StreamProcessor,
		private urlBuilder: StreamUrlBuilder,
		private errorHandler: StreamErrorHandler,
	) {}

	async handle(request: IChatStreamRequest, options: IStreamingOptions, signal: AbortSignal): Promise<void> {
		for (let retry = 0; retry < this.maxRetries; retry++) {
			try {
				await this.performStream(request, options, signal);
				return;
			} catch (error) {
				if (signal.aborted) return;
				if (retry === this.maxRetries - 1) {
					options.onError?.(this.errorHandler.create(error, `Falha após ${this.maxRetries} tentativas`));
					throw error;
				}
				await new Promise(resolve => setTimeout(resolve, 1000 * (retry + 1)));
			}
		}
	}

	private async performStream(request: IChatStreamRequest, options: IStreamingOptions, signal: AbortSignal) {
		const timeoutId = setTimeout(() => signal?.throwIfAborted?.(), this.chunkTimeout);

		try {
			// Obter cookies para autenticação
			const headers: Record<string, string> = {
				"Content-Type": "application/json",
				Accept: "text/event-stream",
				"Cache-Control": "no-cache",
				Connection: "keep-alive",
			};

			// Adicionar cookies se disponíveis
			try {
				const cookiesData = await getAllCookies();
				const cookieService = new CookieHeaderService();
				const cookieHeader = cookieService.processCookieData(cookiesData);

				if (cookieHeader && cookieHeader.trim().length > 0) {
					headers.Cookie = cookieHeader;
				}
			} catch (error) {
				console.warn("Erro ao obter cookies para streaming:", error);
			}

			const response = await fetch(this.urlBuilder.build(), {
				method: "POST",
				headers,
				body: JSON.stringify(request),
				signal,
				mode: "cors",
			});

			if (!response.ok) this.errorHandler.throwHttp(response);
			if (!response.body) throw new Error("Nenhuma resposta recebida do servidor");

			console.log("Stream response received, starting processing...");
			console.log("ReadableStream:", response.body);
			await this.processor.process(response.body, options);
		} finally {
			clearTimeout(timeoutId);
		}
	}
}
