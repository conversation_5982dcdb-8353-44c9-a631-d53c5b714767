"use client";
import { IChatStreamRequest, IStreamingOptions } from "../../types/streaming.type";
import { StreamErrorHandler } from "./error-handler.service";
import { StreamProcessor } from "./streaming-processor";
import { StreamUrlBuilder } from "./url-builder.service";

export class StreamRequestHandler {
	private readonly chunkTimeout = 30000;
	private readonly maxRetries = 3;

	constructor(
		private processor: StreamProcessor,
		private urlBuilder: StreamUrlBuilder,
		private errorHandler: StreamErrorHandler,
	) {}

	async handle(request: IChatStreamRequest, options: IStreamingOptions, signal: AbortSignal): Promise<void> {
		for (let retry = 0; retry < this.maxRetries; retry++) {
			try {
				await this.performStream(request, options, signal);
				return;
			} catch (error) {
				if (signal.aborted) return;
				if (retry === this.maxRetries - 1) {
					options.onError?.(this.errorHandler.create(error, `<PERSON>alha após ${this.maxRetries} tentativas`));
					throw error;
				}
				await new Promise(resolve => setTimeout(resolve, 1000 * (retry + 1)));
			}
		}
	}

	private async performStream(request: IChatStreamRequest, options: IStreamingOptions, signal: AbortSignal) {
		const timeoutId = setTimeout(() => signal?.throwIfAborted?.(), this.chunkTimeout);

		try {
			const response = await fetch(this.urlBuilder.build(), {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					Accept: "text/event-stream",
					"Cache-Control": "no-cache",
					Connection: "keep-alive",
				},
				body: JSON.stringify(request),
				signal,
				credentials: "include",
			});

			if (!response.ok) this.errorHandler.throwHttp(response);
			if (!response.body) throw new Error("Nenhuma resposta recebida do servidor");

			console.log("Stream response received, starting processing...");
			console.log("ReadableStream:", response.body);
			await this.processor.process(response.body, options);
		} finally {
			clearTimeout(timeoutId);
		}
	}
}
