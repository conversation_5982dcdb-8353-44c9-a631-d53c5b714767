// import { RefObject } from "react";
// import { chatStreamService } from "../../services/streaming-ia.service";
// import { IChatError, IChatMessage, IChatStreamRequest } from "../../types/chat.types";
// import { handleStreamingError } from "./handle-stream-error.lib";
// import { handleStreamingChunk } from "./handle-streaming-chunk.lib";

// export async function streamMessage(
// 	content: string,
// 	sessionId: string,
// 	addMessage: (message: Omit<IChatMessage, "id" | "timestamp">) => string | undefined,
// 	updateMessage: (id: string, updates: Partial<IChatMessage>) => void,
// 	setStreaming: (state: boolean, assistantMessageId?: string) => void,
// 	setError: (err: string | null) => void,
// 	streamingIdRef: RefObject<string | null>,
// 	accumulatedContentRef: RefObject<string>,
// ) {
// 	setError(null);
// 	addMessage({
// 		content: content.trim(),
// 		role: "user",
// 	});

// 	const assistantMessageId = addMessage({
// 		content: "",
// 		role: "assistant",
// 		isStreaming: true,
// 	});

// 	if (!assistantMessageId) throw new Error("Erro ao criar mensagem do assistente");

// 	setStreaming(true, assistantMessageId);
// 	streamingIdRef.current = assistantMessageId;
// 	accumulatedContentRef.current = "";

// 	const request: IChatStreamRequest = {
// 		message: content.trim(),
// 		sessionId,
// 	};

// 	await chatStreamService.streamChat(request, {
// 		onChunk: (chunk: string) => handleStreamingChunk(chunk, accumulatedContentRef, updateMessage, assistantMessageId),
// 		onError: (error: IChatError) =>
// 			handleStreamingError(error, updateMessage, setStreaming, setError, assistantMessageId, streamingIdRef, accumulatedContentRef),
// 	});
// }
