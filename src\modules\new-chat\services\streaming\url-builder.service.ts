import { axiosInstance } from "../../../../config/api/instance";
import { CHAT_ENDPOINTS } from "../../api/endpoints";

export class StreamUrlBuilder {
	build(): string {
		// Conecta diretamente ao backend de IA para streaming
		const backendUrl = axiosInstance.defaults.baseURL || "http://localhost:3000"; // Use a URL padrão se não estiver definida
		return `${backendUrl}${CHAT_ENDPOINTS.TO_TALK_STREAM}`;
	}
}
