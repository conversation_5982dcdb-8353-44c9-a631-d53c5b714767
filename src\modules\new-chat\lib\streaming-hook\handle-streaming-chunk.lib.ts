// import { RefObject } from "react";
// import { IChatMessage } from "../../types/chat.types";

// export function handleStreamingChunk(
// 	chunk: string,
// 	accumulatedContentRef: RefObject<string>,
// 	updateMessage: (id: string, updates: Partial<IChatMessage>) => void,
// 	assistantMessageId: string,
// ) {
// 	accumulatedContentRef.current += chunk;
// 	updateMessage(assistantMessageId, {
// 		content: accumulatedContentRef.current,
// 		isStreaming: true,
// 	});
// }
