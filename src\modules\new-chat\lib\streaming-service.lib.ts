import { IChatStreamResponse } from "../types/streaming.type";

export function processBuffer(buffer: string) {
	const lines = buffer.split("\n");
	const remainingBuffer = lines.pop() || "";
	const processedLines = lines.filter(line => line.trim());
	return { processedLines, remainingBuffer };
}

export function processStreamLine(line: string): IChatStreamResponse | undefined {
	const trimmed = line.trim();
	if (!trimmed) return undefined;

	// Remove prefixo "data:" se existir
	let jsonStr = trimmed;
	if (trimmed.startsWith("data:")) {
		jsonStr = trimmed.slice(5).trim();
	}

	// Ignora linhas de controle do SSE
	if (jsonStr === "[DONE]" || jsonStr.startsWith(":")) {
		return undefined;
	}

	try {
		const parsed = JSON.parse(jsonStr);

		// Captura conteúdo mesmo que parcial (tipo delta)
		const content = parsed?.content ?? parsed?.delta?.content ?? parsed?.choices?.[0]?.delta?.content ?? parsed?.text ?? "";

		// Retorna apenas se houver conteúdo válido
		if (content !== undefined && content !== null) {
			return { content: String(content), type: parsed?.type };
		}

		// Se não há conteúdo mas tem tipo, ainda processa (pode ser mensagem de controle)
		if (parsed?.type) {
			return { content: "", type: parsed.type };
		}

		return undefined;
	} catch {
		// Se não for JSON válido, tenta processar como texto simples
		if (jsonStr && !jsonStr.startsWith("{") && !jsonStr.startsWith("[")) {
			return { content: jsonStr, type: undefined };
		}
		return undefined;
	}
}
