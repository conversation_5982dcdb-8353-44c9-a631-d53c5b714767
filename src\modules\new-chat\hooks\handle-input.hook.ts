import { useCallback } from "react";

interface ChatInputState {
	value: string;
	onChange: (value: string) => void;
	onSend: (content: string) => Promise<void>;
	onStop?: () => void;
	disabled?: boolean;
	onMessageSent?: () => void;
}

export const useChatInput = ({ value, onSend, onStop, disabled, onMessageSent }: ChatInputState) => {
	const canSend = !disabled && value.trim().length > 0;
	const canStop = onStop;

	const handleSend = useCallback(async () => {
		if (canSend) {
			try {
				await onSend(value);
				onMessageSent?.();
			} catch (error) {
				console.error("Error sending message:", error);
			}
		}
	}, [canSend, onSend, value, onMessageSent]);

	const handleKeyDown = useCallback(
		(e: React.KeyboardEvent<HTMLTextAreaElement>) => {
			if (e.key === "Enter" && !e.shiftKey) {
				e.preventDefault();
				handleSend();
			}
		},
		[handleSend],
	);

	return {
		canSend,
		canStop,
		handleKeyDown,
		handleSend,
	};
};
