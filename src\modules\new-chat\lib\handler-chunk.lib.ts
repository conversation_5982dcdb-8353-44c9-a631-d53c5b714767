import { IChatMessage } from "../types/messages.type";
import { IChatStreamResponse } from "../types/streaming.type";

interface IStreamingChunkHandler {
	chunkData: IChatStreamResponse;
	updateMessage: (id: string, updates: Partial<IChatMessage>) => void;
	assistantMessageId: string;
	currentValue: string;
}

export function handleStreamingChunk({ chunkData, updateMessage, assistantMessageId, currentValue }: IStreamingChunkHandler) {
	const messageUpdates: Partial<IChatMessage> = {
		content: currentValue, // currentValue já contém o conteúdo acumulado
	};

	// Define o estado de streaming baseado no tipo
	if (chunkData.type === "session") {
		messageUpdates.isStreaming = true;
	} else if (chunkData.type === "complete") {
		messageUpdates.isStreaming = false;
	} else {
		// Se não há tipo definido, assume que ainda está streamando
		messageUpdates.isStreaming = true;
	}

	// Atualiza a mensagem com o novo conteúdo
	updateMessage(assistantMessageId, messageUpdates);
}
