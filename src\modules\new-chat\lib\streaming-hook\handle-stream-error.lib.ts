// import { RefObject } from "react";
// import { toast } from "../../../../core/toast";
// import { IChatError, IChatMessage } from "../../types/chat.types";

// export function handleStreamingError(
// 	error: IChatError,
// 	updateMessage: (id: string, data: Partial<IChatMessage>) => void,
// 	setStreaming: (state: boolean) => void,
// 	setError: (err: string | null) => void,
// 	assistantMessageId: string,
// 	streamingIdRef: RefObject<string | null>,
// 	accumulatedContentRef: RefObject<string>,
// ) {
// 	updateMessage(assistantMessageId, {
// 		content: error.message || "Erro ao processar resposta",
// 		isStreaming: false,
// 		isError: true,
// 	});
// 	setStreaming(false);
// 	setError(error.message || "Erro ao processar resposta");
// 	streamingIdRef.current = null;
// 	accumulatedContentRef.current = "";
// 	toast.error("Erro no chat: " + error.message);
// }
